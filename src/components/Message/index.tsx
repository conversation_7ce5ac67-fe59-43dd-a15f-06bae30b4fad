import React from "react";
import ReactDOM from "react-dom/client";

const Message: React.FC = () => {
  return <div>Message消息提示</div>;
};

// 消息队列管理
class MessageManager {
  private messages: Array<{
    id: string;
    container: HTMLElement;
    root: ReactDOM.Root;
  }> = [];
  private messageHeight = 50; // 每个消息的高度
  private messageGap = 10; // 消息之间的间距

  showMessage() {
    const messageId = `message-${Date.now()}-${Math.random()}`;
    const messageContainer = document.createElement("div");
    messageContainer.className = "message-container";
    messageContainer.id = messageId;

    // 计算当前消息的位置
    const topPosition =
      10 + this.messages.length * (this.messageHeight + this.messageGap);

    messageContainer.style.position = "fixed";
    messageContainer.style.top = `${topPosition}px`;
    messageContainer.style.left = "50%";
    messageContainer.style.transform = "translateX(-50%)";
    messageContainer.style.zIndex = "9999";
    messageContainer.style.backgroundColor = "#fff";
    messageContainer.style.padding = "12px 20px";
    messageContainer.style.borderRadius = "4px";
    messageContainer.style.boxShadow = "0 2px 8px rgba(0, 0, 0, 0.1)";
    messageContainer.style.border = "1px solid #d9d9d9";
    messageContainer.style.fontSize = "14px";
    messageContainer.style.color = "#333";
    messageContainer.style.transition = "all 0.3s ease";

    document.body.appendChild(messageContainer);

    const root = ReactDOM.createRoot(messageContainer);
    root.render(<Message />);

    // 添加到消息队列
    this.messages.push({
      id: messageId,
      container: messageContainer,
      root: root,
    });

    // 2秒后移除消息
    setTimeout(() => {
      this.removeMessage(messageId);
    }, 2000);
  }

  private removeMessage(messageId: string) {
    const messageIndex = this.messages.findIndex((msg) => msg.id === messageId);
    if (messageIndex === -1) return;

    const message = this.messages[messageIndex];

    // 添加淡出动画
    message.container.style.opacity = "0";
    message.container.style.transform = "translateX(-50%) translateY(-20px)";

    setTimeout(() => {
      // 卸载React组件并移除DOM元素
      message.root.unmount();
      message.container.remove();

      // 从队列中移除
      this.messages.splice(messageIndex, 1);

      // 重新计算剩余消息的位置
      this.updateMessagesPosition();
    }, 300); // 等待淡出动画完成
  }

  private updateMessagesPosition() {
    this.messages.forEach((message, index) => {
      const topPosition = 10 + index * (this.messageHeight + this.messageGap);
      message.container.style.top = `${topPosition}px`;
    });
  }
}

// 创建全局消息管理器实例
const messageManager = new MessageManager();

window.showMessage = () => {
  messageManager.showMessage();
};

declare global {
  interface Window {
    showMessage: () => void;
  }
}

export default Message;
